const express = require('express');
const router = express.Router();

// 接收指纹生成报告
router.post('/', async (req, res) => {
  try {
    const report = req.body;

    // 判断是否为缓存指纹（通过检查组件值是否为'cached'）
    const isCachedFingerprint = report.components.canvas.value === 'cached' ||
                               report.components.userAgent.value === 'cached' ||
                               report.components.screenResolution.value === 'cached';

    console.log('=== 指纹生成报告 ===');
    console.log('🔍 [指纹报告] 指纹类型:', isCachedFingerprint ? '缓存指纹' : '新生成指纹');
    console.log('🔍 [指纹报告] 指纹值:', report.fingerprint);

    // 分析各组件状态
    const components = report.components;
    
    console.log('VisitorId:', {
      success: components.visitorId.success,
      value: components.visitorId.value,
      error: components.visitorId.error
    });
    
    console.log('Canvas组件:', {
      success: components.canvas.success,
      value: components.canvas.value,
      usedRandom: components.canvas.usedRandom,
      error: components.canvas.error
    });
    
    console.log('UserAgent组件:', {
      success: components.userAgent.success,
      value: components.userAgent.value,
      usedRandom: components.userAgent.usedRandom,
      error: components.userAgent.error
    });
    
    console.log('屏幕分辨率组件:', {
      success: components.screenResolution.success,
      value: components.screenResolution.value,
      usedRandom: components.screenResolution.usedRandom,
      usedWindowScreen: components.screenResolution.usedWindowScreen,
      error: components.screenResolution.error
    });
    
    // 统计使用随机值的组件
    const randomComponents = [];
    if (components.canvas.usedRandom) randomComponents.push('Canvas');
    if (components.userAgent.usedRandom) randomComponents.push('UserAgent');
    if (components.screenResolution.usedRandom) randomComponents.push('ScreenResolution');
    
    if (randomComponents.length > 0) {
      console.log('⚠️  使用随机值的组件:', randomComponents.join(', '));
    } else {
      console.log('✅ 所有组件都成功获取真实数据');
    }
    
    // 统计失败的组件
    const failedComponents = [];
    if (!components.canvas.success) failedComponents.push('Canvas');
    if (!components.userAgent.success) failedComponents.push('UserAgent');
    if (!components.screenResolution.success) failedComponents.push('ScreenResolution');
    
    if (failedComponents.length > 0) {
      console.log('❌ 获取失败的组件:', failedComponents.join(', '));
    }
    
    console.log('=== 报告结束 ===\n');
    
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('处理指纹报告失败:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
