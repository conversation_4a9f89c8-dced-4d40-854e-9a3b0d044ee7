const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');
const { asyncHandler } = require('../utils/asyncHandler');
const { getClientIP } = require('../utils/ipUtils');

/**
 * @api {post} /api/anonymous/check-eligibility 检查匿名用户是否可以使用免费占卜
 * @apiName CheckAnonymousEligibility
 * @apiGroup Anonymous
 * @apiDescription 检查浏览器指纹是否已使用过免费占卜机会
 *
 * @apiParam {String} fingerprint 浏览器指纹
 *
 * @apiSuccess {Boolean} canUse 是否可以使用免费占卜
 * @apiSuccess {Boolean} hasUsed 是否已使用过免费占卜
 */
router.post('/check-eligibility', asyncHandler(async (req, res) => {
  try {
    const { fingerprint } = req.body;

    if (!fingerprint) {
      return res.status(400).json({ error: '缺少浏览器指纹' });
    }

    const pool = await getConnection();

    // 获取客户端IP地址
    const clientIP = getClientIP(req);

    // 检查该指纹是否已有占卜记录
    const [fingerprintRows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    const fingerprintUsed = fingerprintRows[0].count > 0;

    // 检查该IP是否已有占卜记录
    const [ipRows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE ip_address = ?',
      [clientIP]
    );

    const ipUsed = ipRows[0].count > 0;

    // 任一重复即拒绝（简化逻辑）
    const hasUsed = fingerprintUsed || ipUsed;
    const canUse = !hasUsed;

    // 计算剩余次数
    const remainingReads = canUse ? 1 : 0;

    console.log('📊 [匿名用户剩余占卜次数计算] 最终计算结果:', {
      hasUsed: hasUsed,
      canUse: canUse,
      remainingReads: remainingReads,
      reason: fingerprintUsed ? 'fingerprint已使用' : (ipUsed ? 'IP已使用' : '未使用'),
    });

    res.json({
      canUse: canUse,
      hasUsed: hasUsed,
      fingerprintUsed: fingerprintUsed,
      ipUsed: ipUsed,
      reason: fingerprintUsed ? 'fingerprint' : (ipUsed ? 'ip' : null)
    });
  } catch (error) {
    console.error('❌ [匿名用户剩余占卜次数计算] 检查匿名用户资格时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

/**
 * @api {post} /api/anonymous/record 记录匿名占卜
 * @apiName RecordAnonymousDivination
 * @apiGroup Anonymous
 * @apiDescription 记录匿名用户的占卜信息
 * 
 * @apiParam {String} fingerprint 浏览器指纹
 * @apiParam {String} sessionId 会话ID
 * @apiParam {String} question 占卜问题
 * @apiParam {String} spreadId 牌阵ID
 * @apiParam {String} spreadName 牌阵名称
 * @apiParam {Array} selectedCards 选中的卡牌
 * @apiParam {Object} readingResult 占卜结果
 * @apiParam {Number} inputTokens AI调用输入token数量
 * @apiParam {Number} outputTokens AI调用输出token数量
 * 
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {String} recordId 记录ID
 */
router.post('/record', asyncHandler(async (req, res) => {
  try {
    const {
      fingerprint,
      sessionId,
      question,
      spreadId,
      spreadName,
      selectedCards,
      readingResult,
      inputTokens,
      outputTokens
    } = req.body;

    if (!fingerprint || !sessionId || !question) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const pool = await getConnection();

    // 获取客户端IP地址
    const clientIP = getClientIP(req);

    // 检查是否已经记录过该指纹
    const [fingerprintExisting] = await pool.query(
      'SELECT id FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    if (fingerprintExisting.length > 0) {
      return res.status(400).json({
        error: '该用户已使用过免费占卜机会',
        reason: 'fingerprint'
      });
    }

    // 检查是否已经记录过该IP
    const [ipExisting] = await pool.query(
      'SELECT id FROM anonymous_divination_records WHERE ip_address = ?',
      [clientIP]
    );

    if (ipExisting.length > 0) {
      return res.status(400).json({
        error: '该IP地址已使用过免费占卜机会',
        reason: 'ip'
      });
    }

    // 记录匿名占卜
    const recordId = uuidv4();

    console.log(`[Anonymous] 保存匿名占卜记录 ${recordId}:`, {
      session_id: sessionId,
      spread_id: spreadId || 'NULL',
      spread_name: spreadName || 'NULL',
      fingerprint: fingerprint.substring(0, 20) + '...'
    });

    await pool.query(
      `INSERT INTO anonymous_divination_records
       (id, browser_fingerprint, session_id, question, spread_id, spread_name,
        selected_cards, reading_result, ip_address, input_tokens, output_tokens)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        recordId,
        fingerprint,
        sessionId,
        question,
        spreadId,
        spreadName,
        JSON.stringify(selectedCards),
        JSON.stringify(readingResult),
        clientIP,
        inputTokens || 0,
        outputTokens || 0
      ]
    );

    console.log('匿名占卜记录已保存:', {
      recordId,
      fingerprint: fingerprint.substring(0, 20) + '...',
      sessionId,
      question: question.substring(0, 50) + '...',
      ip: clientIP
    });

    res.json({ success: true, recordId });
  } catch (error) {
    console.error('记录匿名占卜时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

/**
 * @api {get} /api/anonymous/stats 获取匿名占卜统计信息（管理员用）
 * @apiName GetAnonymousStats
 * @apiGroup Anonymous
 * @apiDescription 获取匿名占卜的统计信息
 */
router.get('/stats', asyncHandler(async (req, res) => {
  try {
    const pool = await getConnection();
    
    // 获取今日匿名占卜数量
    const [todayCount] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE DATE(created_at) = CURDATE()'
    );
    
    // 获取总匿名占卜数量
    const [totalCount] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records'
    );
    
    // 获取最近7天的数据
    const [weeklyData] = await pool.query(`
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM anonymous_divination_records
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);

    // 获取tokens统计数据
    const [tokensStats] = await pool.query(`
      SELECT
        SUM(input_tokens) as total_input_tokens,
        SUM(output_tokens) as total_output_tokens,
        COUNT(*) as total_records_with_tokens
      FROM anonymous_divination_records
      WHERE input_tokens IS NOT NULL OR output_tokens IS NOT NULL
    `);

    // 获取今日tokens统计
    const [todayTokensStats] = await pool.query(`
      SELECT
        SUM(input_tokens) as today_input_tokens,
        SUM(output_tokens) as today_output_tokens,
        COUNT(*) as today_records_with_tokens
      FROM anonymous_divination_records
      WHERE DATE(created_at) = CURDATE()
        AND (input_tokens IS NOT NULL OR output_tokens IS NOT NULL)
    `);

    // 计算成本 (参考sessions表的成本计算方式)
    const totalInputTokens = tokensStats[0].total_input_tokens || 0;
    const totalOutputTokens = tokensStats[0].total_output_tokens || 0;
    const todayInputTokens = todayTokensStats[0].today_input_tokens || 0;
    const todayOutputTokens = todayTokensStats[0].today_output_tokens || 0;

    // 使用Qwen的价格: 输入0.0003元/1k tokens, 输出0.0006元/1k tokens
    const totalInputCost = (totalInputTokens / 1000) * 0.0003;
    const totalOutputCost = (totalOutputTokens / 1000) * 0.0006;
    const totalCost = totalInputCost + totalOutputCost;

    const todayInputCost = (todayInputTokens / 1000) * 0.0003;
    const todayOutputCost = (todayOutputTokens / 1000) * 0.0006;
    const todayCost = todayInputCost + todayOutputCost;

    res.json({
      todayCount: todayCount[0].count,
      totalCount: totalCount[0].count,
      weeklyData: weeklyData,
      tokensStats: {
        total: {
          inputTokens: totalInputTokens,
          outputTokens: totalOutputTokens,
          totalTokens: totalInputTokens + totalOutputTokens,
          inputCost: Number(totalInputCost.toFixed(4)),
          outputCost: Number(totalOutputCost.toFixed(4)),
          totalCost: Number(totalCost.toFixed(4)),
          recordsWithTokens: tokensStats[0].total_records_with_tokens || 0
        },
        today: {
          inputTokens: todayInputTokens,
          outputTokens: todayOutputTokens,
          totalTokens: todayInputTokens + todayOutputTokens,
          inputCost: Number(todayInputCost.toFixed(4)),
          outputCost: Number(todayOutputCost.toFixed(4)),
          totalCost: Number(todayCost.toFixed(4)),
          recordsWithTokens: todayTokensStats[0].today_records_with_tokens || 0
        }
      }
    });
  } catch (error) {
    console.error('获取匿名占卜统计时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

module.exports = router;
